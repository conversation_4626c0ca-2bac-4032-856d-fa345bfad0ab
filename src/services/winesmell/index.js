import axios from "axios";

function articleList(data) {
    return axios({
        url: "/api/news/v3/article/getArticleList",
        method: "get",
        params: data
    });
}

function updateStatus(data) {
    return axios({
        url: "/api/news/v3/article/changeArticleStatus",
        method: "post",
        data
    });
}

function articleOperation(data) {
    return axios({
        url: "/api/news/v3/article/operateArticle",
        method: "post",
        data
    });
}

function getArticleType() {
    return axios({
        url: "/api/news/v3/articletype/getArticleType",
        method: "get"
    });
}
function getVestList() {
    // 我的马甲列表
    return axios({
        url: "/api/user/v3/vestuser/get",
        method: "get"
    });
}

function getArticleInfo(data) {
    return axios({
        url: "/api/news/v3/article/getArticleInfo",
        method: "get",
        params: data
    });
}

function delArticle(data) {
    return axios({
        url: "/api/news/v3/article/delArticle",
        method: "post",
        data
    });
}

function articleSort(data) {
    return axios({
        url: "/api/news/v3/article/articleSort",
        method: "post",
        data
    });
}

function getWineSmellComment(data) {
    //  获取酒闻评论列表
    return axios({
        url: "/api/news/v3/articlecom/getArticleCommentList",
        method: "get",
        params: data
    });
}

function getArticleTypeList() {
    // 获取酒闻类型
    return axios({
        url: "/api/news/v3/articletype/getArticleType",
        method: "get"
    });
}
function addArticleType(data) {
    // 新增酒闻类型
    return axios({
        url: "/api/news/v3/articletype/operateArticleType",
        method: "post",
        data
    });
}
function editArticleType(data) {
    // 编辑酒闻类型
    return axios({
        url: "/api/news/v3/articletype/operateArticleType",
        method: "post",
        data
    });
}

function getArticleTypeListTable() {
    // 获取酒闻类型列表
    return axios({
        url: "/api/news/v3/articletype/getArticleTypeList",
        method: "get"
    });
}

function deleteArticleType(data) {
    // 删除酒闻类型列表
    return axios({
        url: "/api/news/v3/articletype/delArticleType",
        method: "post",
        data
    });
}

function sendMakeCommentOn(data) {
    // 发布酒闻评论
    return axios({
        url: "/api/news/v3/articlecom/MakeCommentOn",
        method: "post",
        data
    });
}
function getTopicList(data) {
    // 话题列表
    return axios({
        url: "/api/community/v3/topic/index",
        method: "get",
        params: data
    });
}
function setCommentStatus(data) {
    // 审核设置
    return axios({
        url: "/api/contentaudit/v3/comment/changeStatusBySource",
        method: "post",
        data
    });
}
function upcommentHot(data) {
    // 审核设置
    return axios({
        url: "/api/news/v3/articlecom/changeCommentHotVaule",
        method: "post",
        data
    });
}

export default {
    getArticleTypeList,
    upcommentHot,
    getTopicList,
    deleteArticleType,
    setCommentStatus,
    getWineSmellComment,
    sendMakeCommentOn,
    getArticleTypeListTable,
    addArticleType,
    articleList,
    updateStatus,
    articleOperation,
    getArticleType,
    getArticleInfo,
    editArticleType,
    delArticle,
    articleSort,
    getVestList
};
