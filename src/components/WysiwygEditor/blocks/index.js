// 内容块组件注册
import Vue from "vue";

// 标题块
Vue.component("TitleBlock", {
    props: ["block"],
    render(h) {
        const level = this.block.data.level;
        const content = this.block.data.content;
        const tag = level === "1" ? "h1" : level === "2" ? "h2" : "h3";

        return h("div", { class: "block-title" }, [h(tag, content)]);
    }
});

// 子标题块
Vue.component("SubtitleBlock", {
    props: ["block"],
    render(h) {
        return h("div", { class: "block-subtitle" }, [
            h("h2", this.block.data.content)
        ]);
    }
});

// 日期块
Vue.component("DateBlock", {
    props: ["block"],
    render(h) {
        return h("div", { class: "block-date" }, [
            h("strong", "日期："),
            this.block.data.date
        ]);
    }
});

// 文本块
Vue.component("TextBlock", {
    props: ["block"],
    render(h) {
        const content = this.block.data.content.replace(/\n/g, "<br>");
        return h("div", {
            class: "block-textbox",
            domProps: {
                innerHTML: content
            }
        });
    }
});

// 图片块
Vue.component("ImageBlock", {
    props: ["block"],
    render(h) {
        const elements = [
            h("img", {
                attrs: {
                    src: this.block.data.url,
                    alt: this.block.data.alt
                }
            })
        ];

        if (this.block.data.caption) {
            elements.push(
                h("div", { class: "image-caption" }, this.block.data.caption)
            );
        }

        return h("div", { class: "block-image" }, elements);
    }
});

// 列表块
Vue.component("ListBlock", {
    props: ["block"],
    render(h) {
        const listItems = this.block.data.items.map((item, index) => {
            const children = [item.text];

            if (item.subItems && item.subItems.length > 0) {
                const subList = h(
                    "ul",
                    item.subItems.map(subItem => h("li", subItem))
                );
                children.push(subList);
            }

            return h("li", { key: index }, children);
        });

        return h("div", { class: "block-list" }, [h("ol", listItems)]);
    }
});

// 商品卡片块
Vue.component("ProductBlock", {
    props: ["block"],
    render(h) {
        const elements = [];

        if (this.block.data.imageUrl) {
            elements.push(
                h("div", { class: "product-image" }, [
                    h("img", {
                        attrs: {
                            src: this.block.data.imageUrl,
                            alt: this.block.data.titleCn
                        }
                    })
                ])
            );
        }

        elements.push(
            h("div", { class: "product-title-cn" }, this.block.data.titleCn)
        );

        if (this.block.data.titleEn) {
            elements.push(
                h("div", { class: "product-title-en" }, this.block.data.titleEn)
            );
        }

        elements.push(
            h("div", { class: "product-info" }, [
                h("span", { class: "price" }, `价格：${this.block.data.price}`),
                h("span", { class: "sold" }, `已售：${this.block.data.sold}`)
            ])
        );

        if (this.block.data.description) {
            elements.push(
                h(
                    "div",
                    { class: "product-description" },
                    this.block.data.description
                )
            );
        }

        return h("div", { class: "block-product" }, elements);
    }
});

// 引用块
Vue.component("QuoteBlock", {
    props: ["block"],
    render(h) {
        const elements = [h("div", this.block.data.content)];

        if (this.block.data.source) {
            elements.push(
                h(
                    "div",
                    { class: "quote-source" },
                    `—— ${this.block.data.source}`
                )
            );
        }

        return h("div", { class: "block-quote" }, elements);
    }
});

// 投票块
Vue.component("VoteBlock", {
    props: ["block"],
    render(h) {
        const options = this.block.data.options.map((option, index) => {
            return h("div", { class: "vote-option-item", key: index }, [
                h("input", {
                    attrs: {
                        type:
                            this.block.data.type === "multiple"
                                ? "checkbox"
                                : "radio",
                        name: `vote_${this.block.data.id}`,
                        value: option.value,
                        disabled: true
                    }
                }),
                h("span", option.text)
            ]);
        });

        const infoElements = [];
        if (this.block.data.id) {
            infoElements.push(
                h("span", { class: "vote-id" }, `投票ID: ${this.block.data.id}`)
            );
        }
        if (this.block.data.deadline) {
            infoElements.push(
                h(
                    "span",
                    { class: "vote-deadline" },
                    `截止日期: ${this.block.data.deadline}`
                )
            );
        }

        return h("div", { class: "block-vote" }, [
            h("div", { class: "vote-title" }, this.block.data.title),
            h("div", { class: "vote-options" }, options),
            h("div", { class: "vote-info" }, infoElements)
        ]);
    }
});

// 表格块
Vue.component("TableBlock", {
    props: ["block"],
    render(h) {
        const headers = this.block.data.headers.map((header, index) => {
            return h("th", { key: index }, header);
        });

        const rows = this.block.data.data.map((row, rowIndex) => {
            const cells = row.map((cell, cellIndex) => {
                return h("td", { key: cellIndex }, cell);
            });
            return h("tr", { key: rowIndex }, cells);
        });

        return h("div", { class: "block-table" }, [
            h("table", [h("thead", [h("tr", headers)]), h("tbody", rows)])
        ]);
    }
});
